# 微信聊天机器人使用说明

## 项目简介

基于 wxauto 库和 OpenRouter API 开发的智能微信聊天机器人，能够实时监听微信消息并通过大模型 API 生成智能回复。

## 功能特性

### 🤖 智能对话
- 基于 OpenRouter 平台的大模型 API
- 支持多种模型选择（默认使用 qwen/qwen-32b-chat）
- 智能上下文理解和回复生成

### 💬 消息监听
- 实时监听指定群聊或好友消息
- 支持关键词触发回复（默认：@wxBOT）
- 可配置监听间隔和回复条件

### 🔧 灵活配置
- JSON 配置文件，易于修改
- 支持群聊和私聊分别控制
- 可自定义提示词和回复长度

### 📝 日志记录
- 详细的消息和回复日志
- 支持控制台和文件双重记录
- 便于调试和监控

## 快速开始

### 环境要求
- Python 3.8+
- Windows 10/11
- 微信客户端 ********（推荐）
- OpenRouter API Key

### 安装依赖
```bash
# wxauto 库（如果未安装）
pip install wxauto -i https://pypi.tuna.tsinghua.edu.cn/simple

# openai 库（用于调用 OpenRouter API）
pip install openai
```

### 配置设置

#### 1. API Key 配置
编辑 `bot_config.json` 文件（首次运行会自动创建），设置你的 OpenRouter API Key：

```json
{
  "api_key": "你的OpenRouter API Key",
  "model": "qwen/qwen-32b-chat",
  "site_url": "https://your-site.com",
  "site_name": "WxBot"
}
```

#### 2. 监听对象配置
在配置文件中设置要监听的群聊或好友：

```json
{
  "listen_list": [
    "群聊名称1",
    "群聊名称2", 
    "好友名称",
    "文件传输助手"
  ]
}
```

#### 3. 触发关键词
设置触发机器人回复的关键词：

```json
{
  "trigger_keyword": "@wxBOT"
}
```

### 启动机器人

#### 方式一：双击启动
```
双击 "启动微信机器人.bat" 文件
```

#### 方式二：命令行启动
```bash
python wxbot.py
```

### 使用步骤
1. 确保微信客户端已登录
2. 配置 OpenRouter API Key
3. 设置监听对象列表
4. 启动机器人程序
5. 在群聊或私聊中发送包含触发关键词的消息

## 配置文件说明

### 完整配置示例
```json
{
  "api_key": "sk-or-v1-xxxxxxxxxxxxxxxx",
  "model": "qwen/qwen-32b-chat",
  "site_url": "https://your-site.com",
  "site_name": "WxBot",
  "listen_list": ["文件传输助手"],
  "trigger_keyword": "@wxBOT",
  "wait_interval": 1,
  "max_reply_length": 100,
  "enable_group_chat": true,
  "enable_private_chat": true,
  "log_messages": true
}
```

### 配置项说明

| 配置项 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| api_key | string | OpenRouter API Key | 必填 |
| model | string | 使用的模型名称 | qwen/qwen-32b-chat |
| site_url | string | 网站URL（可选） | https://your-site.com |
| site_name | string | 网站名称（可选） | WxBot |
| listen_list | array | 监听的群聊或好友列表 | ["文件传输助手"] |
| trigger_keyword | string | 触发回复的关键词 | @wxBOT |
| wait_interval | number | 监听间隔（秒） | 1 |
| max_reply_length | number | 最大回复长度 | 100 |
| enable_group_chat | boolean | 是否启用群聊回复 | true |
| enable_private_chat | boolean | 是否启用私聊回复 | true |
| log_messages | boolean | 是否记录消息日志 | true |

## 使用示例

### 群聊使用
在群聊中发送：
```
@wxBOT 今天天气怎么样？
```

机器人会自动回复相关信息。

### 私聊使用
在私聊中发送：
```
@wxBOT 帮我写一个Python函数
```

机器人会根据请求生成相应的回复。

## 支持的模型

OpenRouter 平台支持多种模型，可在配置文件中修改：

- `qwen/qwen-32b-chat` - 通义千问（推荐）
- `openai/gpt-4o` - GPT-4 Omni
- `openai/gpt-3.5-turbo` - GPT-3.5 Turbo
- `anthropic/claude-3-sonnet` - Claude 3 Sonnet
- 更多模型请参考 OpenRouter 官方文档

## 注意事项

### ⚠️ 重要提醒
- 请确保 OpenRouter API Key 有效且有足够余额
- 机器人回复会消耗 API 调用次数
- 建议在测试环境中先验证功能

### 🔧 技术限制
- 需要微信客户端保持登录状态
- 依赖网络连接调用 API
- 受微信版本兼容性影响

### 💡 使用建议
- 首次使用建议先用"文件传输助手"测试
- 合理设置监听间隔，避免过于频繁
- 定期检查日志文件了解运行状态
- 根据需要调整提示词以适应不同场景

## 故障排除

### 常见问题

**Q: 机器人无法连接微信？**
A: 确保微信客户端已登录，版本为 ********，并且没有其他程序占用。

**Q: API 调用失败？**
A: 检查 API Key 是否正确，网络连接是否正常，余额是否充足。

**Q: 机器人不回复消息？**
A: 确认消息包含触发关键词，检查监听对象配置是否正确。

**Q: 回复内容不理想？**
A: 可以修改代码中的 PRESET_PROMPT 提示词来调整回复风格。

### 日志查看
- 控制台日志：实时显示运行状态
- 文件日志：保存在 `wxbot.log` 文件中

## 许可证

本项目基于 MIT 许可证开源。

## 免责声明

本工具仅供学习交流使用，请勿用于非法用途和商业用途！使用时请遵守微信使用条款和相关法律法规。

---

**版本**: 1.0.0  
**基于**: wxauto ******** + OpenRouter API
