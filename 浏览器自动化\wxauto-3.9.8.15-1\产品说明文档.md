# wxauto 产品说明文档

## 产品概述

**wxauto** 是一个专为 Windows 平台微信客户端设计的自动化操作库，基于 UI 自动化技术实现微信消息的自动发送、接收、文件传输等功能。该产品适用于微信 3.9.8.15 版本，支持简体中文、繁体中文和英文界面。

### 核心特性
- 🤖 **智能消息处理**：自动发送/接收文本消息、图片、文件
- 👥 **好友管理**：自动接受好友申请、获取好友列表、群成员管理
- 📱 **多窗口支持**：支持独立聊天窗口监听和操作
- 🌍 **多语言支持**：支持简体中文、繁体中文、英文界面
- 🔄 **实时监听**：支持新消息监听和自动回复
- 💾 **数据保存**：自动保存聊天图片和文件

## 技术架构

### 核心模块结构
```
wxauto/
├── __init__.py          # 主入口，导出 WeChat 类
├── wxauto.py           # 核心 WeChat 类实现
├── elements.py         # UI 元素封装类
├── utils.py            # 工具函数集合
├── languages.py        # 多语言支持
├── errors.py           # 自定义异常类
└── color.py            # 颜色处理工具
```

### 依赖库
- **uiautomation**: UI 自动化核心库
- **Pillow**: 图像处理和截图
- **pywin32**: Windows API 调用
- **psutil**: 系统进程管理
- **pyperclip**: 剪贴板操作

## 核心功能模块

### 1. WeChat 主类 (wxauto.py)
**功能描述**: 微信自动化的核心控制类

**主要方法**:
- `__init__(language='cn')`: 初始化微信实例，支持语言选择
- `ChatWith(who)`: 打开指定好友/群聊窗口
- `SendMsg(msg, who=None)`: 发送文本消息
- `SendFiles(filepath, who=None)`: 发送文件或图片
- `GetAllMessage(savepic=False)`: 获取当前聊天记录
- `GetSessionList()`: 获取聊天列表
- `GetNewFriends()`: 获取新好友申请
- `AddListenChat(who)`: 添加监听对象
- `GetListenMessage()`: 获取监听消息

### 2. 聊天窗口类 (elements.py - ChatWnd)
**功能描述**: 独立聊天窗口的操作封装

**主要功能**:
- 独立窗口消息发送
- 新消息实时获取
- 聊天记录管理
- 群成员获取

### 3. 图片处理类 (elements.py - WeChatImage)
**功能描述**: 微信图片查看和处理

**主要功能**:
- 图片保存和下载
- OCR 文字识别
- 图片导航（上一张/下一张）
- 二维码识别

### 4. 好友管理类 (elements.py - NewFriendsElement)
**功能描述**: 新好友申请处理

**主要功能**:
- 自动接受好友申请
- 设置备注和标签
- 批量处理好友请求

### 5. 通讯录管理类 (elements.py - ContactWnd)
**功能描述**: 通讯录窗口操作

**主要功能**:
- 获取所有好友列表
- 好友搜索功能
- 好友信息提取

## 工具函数库 (utils.py)

### 系统交互工具
- `FindWindow()`: 查找窗口句柄
- `GetPathByHwnd()`: 获取进程路径
- `GetVersionByPath()`: 获取文件版本信息
- `Click()`: 模拟鼠标点击

### 剪贴板操作
- `SetClipboardText()`: 设置剪贴板文本
- `SetClipboardFiles()`: 设置剪贴板文件
- `ReadClipboardData()`: 读取剪贴板数据

### 图像处理
- `IsRedPixel()`: 检测红色像素（新消息提示）
- `ImageGrab`: 屏幕截图功能

### 时间处理
- `ParseWeChatTime()`: 微信时间格式转换

## 多语言支持 (languages.py)

### 支持的语言
- **简体中文 (cn)**: 默认语言
- **繁体中文 (cn_t)**: 港台地区
- **英文 (en)**: 国际版本

### 语言覆盖范围
- 导航栏元素
- 聊天界面元素
- 消息类型标识
- 图片操作界面
- 文件管理界面
- 警告和提示信息

## 使用场景

### 1. 客服自动回复
```python
# 监听客服群消息并自动回复
wx = WeChat()
wx.AddListenChat('客服群')
while True:
    msgs = wx.GetListenMessage()
    for chat, messages in msgs.items():
        for msg in messages:
            if '帮助' in msg[1]:
                wx.SendMsg('请问有什么可以帮助您的？', chat)
```

### 2. 文件批量分发
```python
# 向多个群发送文件
wx = WeChat()
groups = ['工作群A', '工作群B', '项目组']
files = ['D:/report.pdf', 'D:/data.xlsx']
for group in groups:
    wx.SendFiles(files, group)
```

### 3. 好友申请自动处理
```python
# 自动接受好友申请并设置标签
wx = WeChat()
new_friends = wx.GetNewFriends()
for friend in new_friends:
    friend.Accept(remark=f'客户_{friend.name}', tags=['客户', '2024'])
```

## 版本兼容性

### 推荐版本
- **微信版本**: 3.9.8.15 (完全兼容)
- **Python版本**: 3.8+ (不支持 3.7.6 和 3.8.1)
- **操作系统**: Windows 10/11/Server 2016+

### 版本检查机制
系统会自动检测当前微信版本，如版本不匹配会显示警告信息，但基础功能仍可正常使用。

## 安全与限制

### 使用限制
- 仅支持 Windows 平台
- 需要微信客户端已登录
- 部分功能依赖微信界面元素，版本更新可能影响兼容性
- 不支持微信网页版

### 安全考虑
- 所有操作基于 UI 自动化，不涉及微信协议破解
- 不存储用户密码或敏感信息
- 遵循微信使用条款，建议合理使用

## 许可证

本项目采用 MIT 许可证，允许自由使用、修改和分发。

**免责声明**: 代码仅供交流学习使用，请勿用于非法用途和商业用途！如因此产生任何法律纠纷，均与作者无关！

## 技术支持

- **GitHub**: https://github.com/cluic/wxauto
- **文档**: 详见使用文档.md
- **问题反馈**: 通过 GitHub Issues 提交
- **交流群**: 添加作者微信获取群邀请

---

*最后更新: 2024-03-21*  
*版本: 3.9.8.15*
