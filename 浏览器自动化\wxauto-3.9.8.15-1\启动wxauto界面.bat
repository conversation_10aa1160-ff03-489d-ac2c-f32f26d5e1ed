@echo off
chcp 65001 >nul
title wxauto UI界面启动器

echo ========================================
echo    wxauto UI界面启动器 v3.9.8.15
echo ========================================
echo.
echo 正在启动wxauto图形化界面...
echo.
echo 注意事项:
echo 1. 请确保微信客户端已登录
echo 2. 建议使用微信版本 3.9.8.15
echo 3. 首次使用请先在"连接状态"页面连接微信
echo.

python wxauto.py

if %errorlevel% neq 0 (
    echo.
    echo 启动失败！可能的原因：
    echo 1. Python未安装或未添加到PATH
    echo 2. 缺少必要的依赖库
    echo 3. 微信版本不兼容
    echo.
    echo 请检查以上问题后重试
    pause
)
