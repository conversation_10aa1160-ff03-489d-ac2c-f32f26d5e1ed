# Quick Prompt Chrome 扩展开发文档

## 项目概述

Quick Prompt 是一个 Chrome 浏览器扩展，用于在网页输入框中通过指令快速插入预设的 Prompt 内容。该扩展基于 React + TypeScript 开发，使用 WXT 框架构建。

## 技术栈

- **框架**: WXT (Web Extension Toolkit)
- **前端**: React 19.1.0 + TypeScript
- **样式**: Tailwind CSS 4.1.4
- **构建工具**: Vite + pnpm
- **存储**: Chrome Storage API
- **权限**: storage, contextMenus, identity

## 项目结构

```
quick-prompt/
├── .output/chrome-mv3/           # 构建输出目录
│   ├── manifest.json            # 扩展清单文件
│   ├── popup.html               # 弹窗页面
│   ├── options.html             # 选项页面
│   ├── background.js            # 后台脚本
│   ├── content-scripts/         # 内容脚本
│   │   └── content.js
│   ├── chunks/                  # 代码分块
│   │   ├── popup-*.js
│   │   ├── options-*.js
│   │   └── browser-*.js
│   ├── assets/                  # 静态资源
│   │   ├── *.css               # 样式文件
│   │   └── icon-*.png          # 图标文件
│   └── icon/                   # 扩展图标
│       ├── 16.png
│       ├── 32.png
│       ├── 48.png
│       └── 128.png
```

## 核心功能模块

### 1. Manifest 配置 (manifest.json)

```json
{
  "manifest_version": 3,
  "name": "Quick Prompt",
  "description": "在网页输入框中通过指令快速插入预设的 Prompt 内容。",
  "version": "0.0.11",
  "permissions": ["storage", "contextMenus", "identity"],
  "commands": {
    "open-prompt-selector": {
      "suggested_key": {
        "default": "Ctrl+Shift+P",
        "mac": "Command+Shift+P"
      },
      "description": "打开提示词选择弹窗"
    },
    "save-selected-prompt": {
      "suggested_key": {
        "default": "Ctrl+Shift+S", 
        "mac": "Command+Shift+S"
      },
      "description": "保存选中的文本作为提示词"
    }
  }
}
```

### 2. 后台脚本 (background.js)

**主要功能:**
- 提示词数据管理 (userPrompts, userCategories)
- Notion 同步功能
- Google 身份验证
- 右键菜单管理
- 快捷键处理
- 消息通信

**核心 API:**
```javascript
// 获取提示词
chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
  if (message.action === "getPrompts") {
    // 返回启用的提示词列表
  }
});

// 打开选项页
if (message.action === "openOptionsPage") {
  chrome.tabs.create({url: chrome.runtime.getURL("/options.html")});
}

// Notion 同步
if (message.action === "syncToNotion") {
  // 同步本地提示词到 Notion
}
```

### 3. 内容脚本 (content.js)

**主要功能:**
- 监听快捷键事件
- 在页面中注入提示词选择器
- 处理文本选择和插入
- 与后台脚本通信

**核心功能:**
```javascript
// 监听来自后台的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "openPromptSelector") {
    // 显示提示词选择弹窗
  }
  if (message.action === "getSelectedText") {
    // 获取页面选中的文本
  }
});
```

### 4. 弹窗页面 (popup.html)

**功能:**
- 快速访问常用提示词
- 搜索和筛选提示词
- 快速插入到当前页面
- 跳转到选项页面

**技术实现:**
- React 组件化开发
- Tailwind CSS 样式
- 支持深色模式

### 5. 选项页面 (options.html)

**功能:**
- 提示词的增删改查
- 分类管理
- Notion 集成配置
- 导入/导出功能
- 同步状态管理

## 数据存储结构

### 提示词数据 (userPrompts)
```javascript
{
  id: "unique-id",
  title: "提示词标题", 
  content: "提示词内容",
  tags: ["标签1", "标签2"],
  enabled: true,
  categoryId: "分类ID",
  createdAt: "2024-01-01T00:00:00.000Z",
  updatedAt: "2024-01-01T00:00:00.000Z"
}
```

### 分类数据 (userCategories)
```javascript
{
  id: "category-id",
  name: "分类名称",
  description: "分类描述", 
  color: "#6366f1",
  enabled: true,
  createdAt: "2024-01-01T00:00:00.000Z",
  updatedAt: "2024-01-01T00:00:00.000Z"
}
```

## Notion 集成

### API 配置
- **API Key**: 存储在 `chrome.storage.sync`
- **Database ID**: 存储在 `chrome.storage.sync`
- **同步方向**: 双向同步支持

### 数据库结构要求
```javascript
{
  "Title": { "type": "title" },
  "Content": { "type": "rich_text" },
  "Tags": { "type": "multi_select" },
  "Enabled": { "type": "checkbox" },
  "PromptID": { "type": "rich_text" },
  "CategoryID": { "type": "rich_text" }
}
```

## 开发环境搭建

### 1. 环境要求
- Node.js 16+
- pnpm 包管理器
- Chrome 浏览器

### 2. 安装依赖
```bash
git clone https://github.com/wenyuanw/quick-prompt.git
cd quick-prompt
pnpm install
```

### 3. 开发模式
```bash
pnpm dev          # Chrome 开发模式
pnpm dev:firefox  # Firefox 开发模式
```

### 4. 构建生产版本
```bash
pnpm build        # 构建 Chrome 版本
pnpm build:firefox # 构建 Firefox 版本
```

### 5. 打包扩展
```bash
pnpm zip          # 打包 Chrome 版本
pnpm zip:firefox  # 打包 Firefox 版本
```

## 安装和调试

### 1. 加载开发版本
1. 打开 Chrome 扩展管理页面: `chrome://extensions`
2. 启用"开发者模式"
3. 点击"加载已解压的扩展"
4. 选择 `.output/chrome-mv3/` 目录

### 2. 调试方法
- **后台脚本**: 在扩展管理页面点击"检查视图"
- **内容脚本**: 在网页中按 F12 打开开发者工具
- **弹窗页面**: 右键扩展图标选择"检查弹出内容"
- **选项页面**: 直接在选项页面按 F12

## 核心 API 参考

### Chrome Storage API
```javascript
// 获取数据
const data = await chrome.storage.local.get(['userPrompts']);

// 保存数据  
await chrome.storage.local.set({userPrompts: prompts});

// 监听变化
chrome.storage.onChanged.addListener((changes, area) => {
  if (area === 'local' && changes.userPrompts) {
    // 处理提示词数据变化
  }
});
```

### 消息通信
```javascript
// 发送消息
const response = await chrome.runtime.sendMessage({
  action: "getPrompts"
});

// 监听消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 处理消息
  sendResponse({success: true, data: result});
});
```

## 扩展功能

### 1. 快捷键支持
- `Ctrl+Shift+P` (Mac: `Cmd+Shift+P`): 打开提示词选择器
- `Ctrl+Shift+S` (Mac: `Cmd+Shift+S`): 保存选中文本为提示词

### 2. 右键菜单
- "保存该提示词": 将选中文本保存为新提示词

### 3. 变量支持
- 支持 `{{变量名}}` 格式的动态变量
- 在插入时可以替换为实际值

## 性能优化

### 1. 代码分割
- 使用 Vite 的代码分割功能
- 按页面分离 JavaScript 代码

### 2. 资源优化
- 图标使用多尺寸 PNG 格式
- CSS 使用 Tailwind 的 purge 功能

### 3. 存储优化
- 使用 Chrome Storage API 的本地存储
- 大数据使用分页加载

## 安全考虑

### 1. 内容安全策略 (CSP)
- 遵循 Manifest V3 的 CSP 要求
- 避免使用 `eval()` 和内联脚本

### 2. 权限最小化
- 只请求必要的权限
- 使用 activeTab 权限模式

### 3. 数据验证
- 对用户输入进行严格验证
- 防止 XSS 攻击

## 发布流程

### 1. 版本管理
- 更新 `package.json` 中的版本号
- 更新 `manifest.json` 中的版本号

### 2. 构建发布版本
```bash
pnpm build
pnpm zip
```

### 3. Chrome Web Store 发布
1. 登录 Chrome 开发者控制台
2. 上传 `.output/chrome-mv3.zip` 文件
3. 填写应用描述和截图
4. 提交审核

## 故障排除

### 1. 常见问题
- **扩展无法加载**: 检查 manifest.json 语法
- **内容脚本不工作**: 检查页面权限和 CSP
- **存储数据丢失**: 检查 storage 权限

### 2. 调试技巧
- 使用 `console.log()` 输出调试信息
- 检查 Chrome 扩展错误页面
- 使用 React DevTools 调试组件

## 贡献指南

### 1. 代码规范
- 使用 TypeScript 严格模式
- 遵循 ESLint 配置
- 使用 Prettier 格式化代码

### 2. 提交规范
- 使用语义化提交信息
- 包含详细的变更说明
- 添加相关的测试用例

---

**版本**: v0.0.11  
**最后更新**: 2024年5月26日  
**维护者**: Quick Prompt 开发团队
