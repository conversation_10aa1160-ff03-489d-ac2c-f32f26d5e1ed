# wxauto UI界面 - 微信自动化图形操作工具

## 项目简介

基于 [wxauto](https://github.com/cluic/wxauto) 库开发的图形化微信自动化操作工具，提供友好的用户界面来执行各种微信自动化任务。

**原版wxauto库**：适用PC微信********版本
**UI界面版本**：基于原版库开发的图形化界面

**********版本微信安装包下载**：
[点击下载](https://github.com/tom-snow/wechat-windows-versions/releases/download/v********/WeChatSetup-********.exe)

**相关文档**：
[wxauto库使用文档](./使用文档.md) |
[UI界面使用说明](./wxauto界面使用说明.md) |
[产品功能文档](./产品说明文档.md)

## 功能特性

### 🤖 智能消息处理
- 自动发送/接收文本消息
- 支持图片和文件传输
- 批量消息发送
- 聊天记录获取和保存

### 👥 好友管理
- 自动接受好友申请
- 批量设置备注和标签
- 获取好友列表
- 好友搜索功能

### 📱 多窗口支持
- 支持独立聊天窗口操作
- 群成员管理
- 页面切换控制

### 🔄 实时监听
- 新消息实时监听
- 智能自动回复
- 关键词匹配回复
- 监听日志记录

### 💾 数据管理
- 自动保存聊天图片
- 日志文件记录
- 批量文件发送

### 🌍 多语言支持
- 简体中文界面
- 繁体中文界面
- 英文界面

## 环境要求

|  环境  | 版本要求 |
| :----: | :--: |
|   OS   | Windows 10/11/Server 2016+ |
|  微信  | ******** (推荐) |
| Python | 3.8+ (不支持3.7.6和3.8.1) |

## 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动UI界面
**方式一：双击启动**
```
双击 "启动wxauto界面.bat" 文件
```

**方式二：命令行启动**
```bash
python wxauto.py
```

### 使用步骤
1. 确保微信客户端已登录
2. 启动wxauto UI界面
3. 在"连接状态"页面连接微信
4. 在各功能页面进行相应操作

## UI界面功能

### 📋 主要页面

| 页面 | 功能描述 |
|------|----------|
| 🔗 连接状态 | 微信连接管理、语言设置、状态监控 |
| 💬 消息操作 | 发送消息、获取聊天记录、新消息检查 |
| 📁 文件操作 | 文件发送、批量文件分发 |
| 👥 好友管理 | 好友申请处理、好友列表管理 |
| 🔄 监听回复 | 消息监听、自动回复设置 |
| 🔧 工具设置 | 群管理、系统工具、参数设置 |

## 原版wxauto库使用示例
<font color=red>**请先登录PC微信客户端**</font>
```python
from wxauto import *

# 获取当前微信客户端
wx = WeChat()

# 获取会话列表
wx.GetSessionList()

# 向某人发送消息（以`文件传输助手`为例）
msg = '你好~'
who = '文件传输助手'
wx.SendMsg(msg, who)  # 向`文件传输助手`发送消息：你好~

# 向某人发送文件（以`文件传输助手`为例，发送三个不同类型文件）
files = [
    'D:/test/wxauto.py',
    'D:/test/pic.png',
    'D:/test/files.rar'
]
who = '文件传输助手'
wx.SendFiles(filepath=files, who=who)  # 向`文件传输助手`发送上述三个文件

# 下载当前聊天窗口的聊天记录及图片
msgs = wx.GetAllMessage(savepic=True)   # 获取聊天记录，及自动下载图片
```
## 文件结构

```
wxauto-********-1/
├── wxauto.py                    # 主UI界面程序
├── 启动wxauto界面.bat            # 启动脚本
├── wxauto界面使用说明.md         # 详细使用说明
├── 产品说明文档.md              # 产品功能文档
├── README.md                   # 项目说明
├── requirements.txt            # 依赖库列表
├── demo.py                     # 功能演示脚本
├── 使用文档.md                  # wxauto库使用文档
├── wxauto/                     # wxauto核心库
│   ├── __init__.py
│   ├── wxauto.py              # 核心功能实现
│   ├── elements.py            # UI元素封装
│   ├── utils.py               # 工具函数
│   ├── languages.py           # 多语言支持
│   ├── errors.py              # 异常处理
│   └── color.py               # 颜色处理
└── utils/                      # 工具资源
    ├── version.png
    ├── wxqrcode.png
    ├── wxpay.png
    └── alipay.png
```

## 使用场景

### 1. 客服自动回复
设置关键词回复规则，自动回复客户咨询

### 2. 文件批量分发
向多个群或好友批量发送文件和通知

### 3. 好友申请自动处理
自动接受好友申请并设置统一的备注和标签

### 4. 消息监听和记录
监听重要群聊消息，自动记录和回复

## 注意事项

### ⚠️ 重要提醒
- **仅供学习交流使用，请勿用于非法用途和商业用途**
- 使用时请遵守微信使用条款
- 建议在测试环境中先进行功能验证

### 🔧 技术限制
- 仅支持Windows平台
- 需要微信客户端已登录
- 部分功能依赖微信界面元素
- 微信版本更新可能影响兼容性

### 💡 使用建议
- 首次使用建议先用"文件传输助手"测试
- 批量操作前先小范围测试
- 定期检查微信版本兼容性
- 保持适当的操作间隔避免被限制

## 技术支持

### 📚 文档资源
- [wxauto界面使用说明](./wxauto界面使用说明.md)
- [产品功能文档](./产品说明文档.md)
- [wxauto库使用文档](./使用文档.md)

### 🐛 问题反馈
- 查看程序日志了解错误信息
- 确认微信版本和系统环境
- 参考官方文档和使用说明
- GitHub Issues: [wxauto项目](https://github.com/cluic/wxauto)

### 🤝 贡献指南
欢迎提交Bug报告、功能建议和代码改进！

## 许可证

本项目基于 MIT 许可证开源。

## 免责声明

代码仅供交流学习使用，请勿用于非法用途和商业用途！如因此产生任何法律纠纷，均与作者无关！

---

**版本**: ********
**最后更新**: 2024年
**基于**: [wxauto](https://github.com/cluic/wxauto) 库开发



