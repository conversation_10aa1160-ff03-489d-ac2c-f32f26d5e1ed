#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
wxauto UI界面 - 微信自动化操作工具
基于产品说明文档创建的图形化操作界面
Author: wxauto UI Generator
Version: ********
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import time
import os
import sys
from datetime import datetime

# 添加wxauto模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from wxauto import WeChat
except ImportError:
    messagebox.showerror("错误", "无法导入wxauto模块，请确保已正确安装依赖库")
    sys.exit(1)

class WxAutoUI:
    def __init__(self, root):
        self.root = root
        self.root.title("wxauto - 微信自动化操作工具 v********")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # 微信实例
        self.wx = None
        self.is_connected = False
        self.listen_thread = None
        self.is_listening = False

        # 创建界面
        self.create_widgets()

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪 - 请先连接微信")
        self.status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # 1. 连接与状态页面
        self.create_connection_tab(notebook)

        # 2. 消息操作页面
        self.create_message_tab(notebook)

        # 3. 文件操作页面
        self.create_file_tab(notebook)

        # 4. 好友管理页面
        self.create_friend_tab(notebook)

        # 5. 监听与自动回复页面
        self.create_listen_tab(notebook)

        # 6. 工具与设置页面
        self.create_tools_tab(notebook)

    def create_connection_tab(self, notebook):
        """连接与状态页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🔗 连接状态")

        # 连接区域
        conn_frame = ttk.LabelFrame(frame, text="微信连接", padding=10)
        conn_frame.pack(fill=tk.X, pady=5)

        # 语言选择
        lang_frame = ttk.Frame(conn_frame)
        lang_frame.pack(fill=tk.X, pady=5)
        ttk.Label(lang_frame, text="界面语言:").pack(side=tk.LEFT)
        self.language_var = tk.StringVar(value="cn")
        lang_combo = ttk.Combobox(lang_frame, textvariable=self.language_var,
                                 values=["cn (简体中文)", "cn_t (繁体中文)", "en (English)"],
                                 state="readonly", width=20)
        lang_combo.pack(side=tk.LEFT, padx=10)

        # 连接按钮
        btn_frame = ttk.Frame(conn_frame)
        btn_frame.pack(fill=tk.X, pady=10)
        self.connect_btn = ttk.Button(btn_frame, text="连接微信", command=self.connect_wechat)
        self.connect_btn.pack(side=tk.LEFT, padx=5)
        self.disconnect_btn = ttk.Button(btn_frame, text="断开连接", command=self.disconnect_wechat, state=tk.DISABLED)
        self.disconnect_btn.pack(side=tk.LEFT, padx=5)

        # 状态显示
        status_frame = ttk.LabelFrame(frame, text="连接状态", padding=10)
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = scrolledtext.ScrolledText(status_frame, height=15, state=tk.DISABLED)
        self.status_text.pack(fill=tk.BOTH, expand=True)

        # 版本信息
        version_frame = ttk.LabelFrame(frame, text="版本信息", padding=10)
        version_frame.pack(fill=tk.X, pady=5)

        version_info = """
wxauto版本: ********
支持微信版本: ******** (推荐)
Python版本要求: 3.8+
操作系统: Windows 10/11/Server 2016+
        """.strip()
        ttk.Label(version_frame, text=version_info, justify=tk.LEFT).pack(anchor=tk.W)

    def create_message_tab(self, notebook):
        """消息操作页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="💬 消息操作")

        # 发送消息区域
        send_frame = ttk.LabelFrame(frame, text="发送消息", padding=10)
        send_frame.pack(fill=tk.X, pady=5)

        # 接收人
        recipient_frame = ttk.Frame(send_frame)
        recipient_frame.pack(fill=tk.X, pady=5)
        ttk.Label(recipient_frame, text="发送给:").pack(side=tk.LEFT)
        self.recipient_var = tk.StringVar(value="文件传输助手")
        recipient_entry = ttk.Entry(recipient_frame, textvariable=self.recipient_var, width=30)
        recipient_entry.pack(side=tk.LEFT, padx=10)
        ttk.Button(recipient_frame, text="获取聊天列表", command=self.get_session_list).pack(side=tk.LEFT, padx=5)

        # 消息内容
        msg_frame = ttk.Frame(send_frame)
        msg_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        ttk.Label(msg_frame, text="消息内容:").pack(anchor=tk.W)
        self.message_text = scrolledtext.ScrolledText(msg_frame, height=6)
        self.message_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # 发送按钮
        send_btn_frame = ttk.Frame(send_frame)
        send_btn_frame.pack(fill=tk.X, pady=5)
        ttk.Button(send_btn_frame, text="发送消息", command=self.send_message).pack(side=tk.LEFT, padx=5)
        ttk.Button(send_btn_frame, text="清空内容", command=lambda: self.message_text.delete(1.0, tk.END)).pack(side=tk.LEFT, padx=5)

        # 获取消息区域
        get_frame = ttk.LabelFrame(frame, text="获取消息", padding=10)
        get_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 获取选项
        get_options_frame = ttk.Frame(get_frame)
        get_options_frame.pack(fill=tk.X, pady=5)
        self.save_pic_var = tk.BooleanVar()
        ttk.Checkbutton(get_options_frame, text="自动保存图片", variable=self.save_pic_var).pack(side=tk.LEFT)
        ttk.Button(get_options_frame, text="获取当前聊天记录", command=self.get_all_messages).pack(side=tk.LEFT, padx=10)
        ttk.Button(get_options_frame, text="获取新消息", command=self.get_new_messages).pack(side=tk.LEFT, padx=5)

        # 消息显示
        self.messages_text = scrolledtext.ScrolledText(get_frame, height=15, state=tk.DISABLED)
        self.messages_text.pack(fill=tk.BOTH, expand=True, pady=5)

    def create_file_tab(self, notebook):
        """文件操作页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="📁 文件操作")

        # 文件发送区域
        send_file_frame = ttk.LabelFrame(frame, text="发送文件", padding=10)
        send_file_frame.pack(fill=tk.X, pady=5)

        # 接收人
        file_recipient_frame = ttk.Frame(send_file_frame)
        file_recipient_frame.pack(fill=tk.X, pady=5)
        ttk.Label(file_recipient_frame, text="发送给:").pack(side=tk.LEFT)
        self.file_recipient_var = tk.StringVar(value="文件传输助手")
        ttk.Entry(file_recipient_frame, textvariable=self.file_recipient_var, width=30).pack(side=tk.LEFT, padx=10)

        # 文件选择
        file_select_frame = ttk.Frame(send_file_frame)
        file_select_frame.pack(fill=tk.X, pady=5)
        ttk.Label(file_select_frame, text="选择文件:").pack(anchor=tk.W)

        self.file_listbox = tk.Listbox(file_select_frame, height=6)
        self.file_listbox.pack(fill=tk.BOTH, expand=True, pady=5)

        file_btn_frame = ttk.Frame(send_file_frame)
        file_btn_frame.pack(fill=tk.X, pady=5)
        ttk.Button(file_btn_frame, text="添加文件", command=self.add_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_btn_frame, text="添加文件夹", command=self.add_folder).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_btn_frame, text="清空列表", command=lambda: self.file_listbox.delete(0, tk.END)).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_btn_frame, text="发送文件", command=self.send_files).pack(side=tk.LEFT, padx=5)

        # 批量发送区域
        batch_frame = ttk.LabelFrame(frame, text="批量发送", padding=10)
        batch_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 接收人列表
        batch_recipient_frame = ttk.Frame(batch_frame)
        batch_recipient_frame.pack(fill=tk.X, pady=5)
        ttk.Label(batch_recipient_frame, text="接收人列表 (每行一个):").pack(anchor=tk.W)

        self.batch_recipients_text = scrolledtext.ScrolledText(batch_recipient_frame, height=8)
        self.batch_recipients_text.pack(fill=tk.BOTH, expand=True, pady=5)

        batch_btn_frame = ttk.Frame(batch_frame)
        batch_btn_frame.pack(fill=tk.X, pady=5)
        ttk.Button(batch_btn_frame, text="批量发送文件", command=self.batch_send_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(batch_btn_frame, text="批量发送消息", command=self.batch_send_messages).pack(side=tk.LEFT, padx=5)

    def create_friend_tab(self, notebook):
        """好友管理页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="👥 好友管理")

        # 新好友申请区域
        new_friends_frame = ttk.LabelFrame(frame, text="新好友申请", padding=10)
        new_friends_frame.pack(fill=tk.X, pady=5)

        new_friends_btn_frame = ttk.Frame(new_friends_frame)
        new_friends_btn_frame.pack(fill=tk.X, pady=5)
        ttk.Button(new_friends_btn_frame, text="获取新好友申请", command=self.get_new_friends).pack(side=tk.LEFT, padx=5)
        ttk.Button(new_friends_btn_frame, text="批量接受好友", command=self.batch_accept_friends).pack(side=tk.LEFT, padx=5)

        # 好友申请列表
        self.new_friends_tree = ttk.Treeview(new_friends_frame, columns=("name", "message", "status"), show="headings", height=8)
        self.new_friends_tree.heading("name", text="昵称")
        self.new_friends_tree.heading("message", text="申请消息")
        self.new_friends_tree.heading("status", text="状态")
        self.new_friends_tree.pack(fill=tk.BOTH, expand=True, pady=5)

        # 接受好友设置
        accept_frame = ttk.Frame(new_friends_frame)
        accept_frame.pack(fill=tk.X, pady=5)
        ttk.Label(accept_frame, text="备注:").pack(side=tk.LEFT)
        self.friend_remark_var = tk.StringVar()
        ttk.Entry(accept_frame, textvariable=self.friend_remark_var, width=20).pack(side=tk.LEFT, padx=5)
        ttk.Label(accept_frame, text="标签:").pack(side=tk.LEFT, padx=(10,0))
        self.friend_tags_var = tk.StringVar()
        ttk.Entry(accept_frame, textvariable=self.friend_tags_var, width=20).pack(side=tk.LEFT, padx=5)
        ttk.Label(accept_frame, text="(多个标签用逗号分隔)").pack(side=tk.LEFT)

        # 好友列表区域
        friends_list_frame = ttk.LabelFrame(frame, text="好友列表", padding=10)
        friends_list_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        friends_btn_frame = ttk.Frame(friends_list_frame)
        friends_btn_frame.pack(fill=tk.X, pady=5)
        ttk.Button(friends_btn_frame, text="获取所有好友", command=self.get_all_friends).pack(side=tk.LEFT, padx=5)
        ttk.Label(friends_btn_frame, text="搜索:").pack(side=tk.LEFT, padx=(20,0))
        self.friend_search_var = tk.StringVar()
        ttk.Entry(friends_btn_frame, textvariable=self.friend_search_var, width=20).pack(side=tk.LEFT, padx=5)
        ttk.Button(friends_btn_frame, text="搜索好友", command=self.search_friends).pack(side=tk.LEFT, padx=5)

        # 好友列表
        self.friends_tree = ttk.Treeview(friends_list_frame, columns=("nickname", "remark", "tags"), show="headings", height=12)
        self.friends_tree.heading("nickname", text="昵称")
        self.friends_tree.heading("remark", text="备注")
        self.friends_tree.heading("tags", text="标签")
        self.friends_tree.pack(fill=tk.BOTH, expand=True, pady=5)

    def create_listen_tab(self, notebook):
        """监听与自动回复页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🔄 监听回复")

        # 监听设置区域
        listen_setup_frame = ttk.LabelFrame(frame, text="监听设置", padding=10)
        listen_setup_frame.pack(fill=tk.X, pady=5)

        # 监听对象
        listen_obj_frame = ttk.Frame(listen_setup_frame)
        listen_obj_frame.pack(fill=tk.X, pady=5)
        ttk.Label(listen_obj_frame, text="监听对象:").pack(side=tk.LEFT)
        self.listen_target_var = tk.StringVar()
        ttk.Entry(listen_obj_frame, textvariable=self.listen_target_var, width=30).pack(side=tk.LEFT, padx=10)
        ttk.Button(listen_obj_frame, text="添加监听", command=self.add_listen_target).pack(side=tk.LEFT, padx=5)

        # 监听列表
        self.listen_listbox = tk.Listbox(listen_setup_frame, height=6)
        self.listen_listbox.pack(fill=tk.X, pady=5)

        listen_btn_frame = ttk.Frame(listen_setup_frame)
        listen_btn_frame.pack(fill=tk.X, pady=5)
        self.start_listen_btn = ttk.Button(listen_btn_frame, text="开始监听", command=self.start_listening)
        self.start_listen_btn.pack(side=tk.LEFT, padx=5)
        self.stop_listen_btn = ttk.Button(listen_btn_frame, text="停止监听", command=self.stop_listening, state=tk.DISABLED)
        self.stop_listen_btn.pack(side=tk.LEFT, padx=5)
        ttk.Button(listen_btn_frame, text="清空列表", command=lambda: self.listen_listbox.delete(0, tk.END)).pack(side=tk.LEFT, padx=5)

        # 自动回复设置
        auto_reply_frame = ttk.LabelFrame(frame, text="自动回复设置", padding=10)
        auto_reply_frame.pack(fill=tk.X, pady=5)

        self.auto_reply_enabled = tk.BooleanVar()
        ttk.Checkbutton(auto_reply_frame, text="启用自动回复", variable=self.auto_reply_enabled).pack(anchor=tk.W)

        # 关键词回复
        keyword_frame = ttk.Frame(auto_reply_frame)
        keyword_frame.pack(fill=tk.X, pady=5)
        ttk.Label(keyword_frame, text="关键词:").pack(side=tk.LEFT)
        self.keyword_var = tk.StringVar()
        ttk.Entry(keyword_frame, textvariable=self.keyword_var, width=20).pack(side=tk.LEFT, padx=5)
        ttk.Label(keyword_frame, text="回复内容:").pack(side=tk.LEFT, padx=(10,0))
        self.reply_var = tk.StringVar()
        ttk.Entry(keyword_frame, textvariable=self.reply_var, width=30).pack(side=tk.LEFT, padx=5)
        ttk.Button(keyword_frame, text="添加规则", command=self.add_reply_rule).pack(side=tk.LEFT, padx=5)

        # 回复规则列表
        self.reply_rules_tree = ttk.Treeview(auto_reply_frame, columns=("keyword", "reply"), show="headings", height=8)
        self.reply_rules_tree.heading("keyword", text="关键词")
        self.reply_rules_tree.heading("reply", text="回复内容")
        self.reply_rules_tree.pack(fill=tk.BOTH, expand=True, pady=5)

        # 监听日志
        log_frame = ttk.LabelFrame(frame, text="监听日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.listen_log_text = scrolledtext.ScrolledText(log_frame, height=10, state=tk.DISABLED)
        self.listen_log_text.pack(fill=tk.BOTH, expand=True)

    def create_tools_tab(self, notebook):
        """工具与设置页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🔧 工具设置")

        # 群管理工具
        group_tools_frame = ttk.LabelFrame(frame, text="群管理工具", padding=10)
        group_tools_frame.pack(fill=tk.X, pady=5)

        group_btn_frame = ttk.Frame(group_tools_frame)
        group_btn_frame.pack(fill=tk.X, pady=5)
        ttk.Button(group_btn_frame, text="获取群成员", command=self.get_group_members).pack(side=tk.LEFT, padx=5)
        ttk.Button(group_btn_frame, text="切换到通讯录", command=self.switch_to_contact).pack(side=tk.LEFT, padx=5)
        ttk.Button(group_btn_frame, text="切换到聊天", command=self.switch_to_chat).pack(side=tk.LEFT, padx=5)

        # 群成员显示
        self.group_members_text = scrolledtext.ScrolledText(group_tools_frame, height=8, state=tk.DISABLED)
        self.group_members_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # 系统工具
        system_tools_frame = ttk.LabelFrame(frame, text="系统工具", padding=10)
        system_tools_frame.pack(fill=tk.X, pady=5)

        system_btn_frame = ttk.Frame(system_tools_frame)
        system_btn_frame.pack(fill=tk.X, pady=5)
        ttk.Button(system_btn_frame, text="检查新消息", command=self.check_new_message).pack(side=tk.LEFT, padx=5)
        ttk.Button(system_btn_frame, text="获取当前聊天窗口", command=self.get_current_chat).pack(side=tk.LEFT, padx=5)
        ttk.Button(system_btn_frame, text="加载更多消息", command=self.load_more_messages).pack(side=tk.LEFT, padx=5)

        # 设置区域
        settings_frame = ttk.LabelFrame(frame, text="设置", padding=10)
        settings_frame.pack(fill=tk.X, pady=5)

        # 监听间隔设置
        interval_frame = ttk.Frame(settings_frame)
        interval_frame.pack(fill=tk.X, pady=5)
        ttk.Label(interval_frame, text="监听间隔(秒):").pack(side=tk.LEFT)
        self.listen_interval_var = tk.StringVar(value="3")
        ttk.Entry(interval_frame, textvariable=self.listen_interval_var, width=10).pack(side=tk.LEFT, padx=5)

        # 日志设置
        log_settings_frame = ttk.Frame(settings_frame)
        log_settings_frame.pack(fill=tk.X, pady=5)
        self.enable_log_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(log_settings_frame, text="启用详细日志", variable=self.enable_log_var).pack(side=tk.LEFT)
        self.save_log_var = tk.BooleanVar()
        ttk.Checkbutton(log_settings_frame, text="保存日志到文件", variable=self.save_log_var).pack(side=tk.LEFT, padx=20)

        # 关于信息
        about_frame = ttk.LabelFrame(frame, text="关于", padding=10)
        about_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        about_text = """
wxauto UI界面 v********

基于wxauto库开发的图形化操作界面
支持微信自动化的各种功能操作

功能特性:
• 智能消息处理 - 自动发送/接收文本消息、图片、文件
• 好友管理 - 自动接受好友申请、获取好友列表
• 多窗口支持 - 支持独立聊天窗口监听和操作
• 实时监听 - 支持新消息监听和自动回复
• 数据保存 - 自动保存聊天图片和文件

使用说明:
1. 首先在"连接状态"页面连接微信
2. 在各功能页面进行相应操作
3. 可在"监听回复"页面设置自动回复
4. 使用"工具设置"页面的辅助功能

注意事项:
• 请确保微信客户端已登录
• 建议使用微信版本 ********
• 仅供学习交流使用，请勿用于非法用途

许可证: MIT License
        """.strip()

        about_label = ttk.Label(about_frame, text=about_text, justify=tk.LEFT)
        about_label.pack(anchor=tk.W, fill=tk.BOTH, expand=True)

        # 初始化变量
        self.reply_rules = {}  # 存储回复规则
        self.listen_targets = []  # 存储监听目标

    # ==================== 连接相关方法 ====================
    def connect_wechat(self):
        """连接微信"""
        try:
            self.log_message("正在连接微信...")
            language = self.language_var.get().split()[0]  # 获取语言代码
            self.wx = WeChat(language=language)
            self.is_connected = True
            self.connect_btn.config(state=tk.DISABLED)
            self.disconnect_btn.config(state=tk.NORMAL)
            self.status_var.set(f"已连接 - {self.wx.nickname}")
            self.log_message(f"微信连接成功！当前用户：{self.wx.nickname}")
        except Exception as e:
            self.log_message(f"连接失败：{str(e)}")
            messagebox.showerror("连接失败", f"无法连接到微信：{str(e)}")

    def disconnect_wechat(self):
        """断开微信连接"""
        try:
            if self.is_listening:
                self.stop_listening()
            self.wx = None
            self.is_connected = False
            self.connect_btn.config(state=tk.NORMAL)
            self.disconnect_btn.config(state=tk.DISABLED)
            self.status_var.set("已断开连接")
            self.log_message("微信连接已断开")
        except Exception as e:
            self.log_message(f"断开连接时出错：{str(e)}")

    def check_connection(self):
        """检查连接状态"""
        if not self.is_connected or not self.wx:
            messagebox.showwarning("未连接", "请先连接微信！")
            return False
        return True

    # ==================== 消息相关方法 ====================
    def send_message(self):
        """发送消息"""
        if not self.check_connection():
            return

        try:
            recipient = self.recipient_var.get().strip()
            message = self.message_text.get(1.0, tk.END).strip()

            if not recipient or not message:
                messagebox.showwarning("输入错误", "请输入接收人和消息内容！")
                return

            self.wx.SendMsg(message, recipient)
            self.log_message(f"消息已发送给 {recipient}: {message[:50]}...")
            messagebox.showinfo("发送成功", f"消息已发送给 {recipient}")

        except Exception as e:
            self.log_message(f"发送消息失败：{str(e)}")
            messagebox.showerror("发送失败", f"发送消息时出错：{str(e)}")

    def get_session_list(self):
        """获取聊天列表"""
        if not self.check_connection():
            return

        try:
            sessions = self.wx.GetSessionList()
            self.log_message("聊天列表获取成功")

            # 显示聊天列表
            session_info = "当前聊天列表:\n"
            for name, count in sessions.items():
                session_info += f"• {name}"
                if count > 0:
                    session_info += f" ({count}条新消息)"
                session_info += "\n"

            self.display_messages(session_info)

        except Exception as e:
            self.log_message(f"获取聊天列表失败：{str(e)}")
            messagebox.showerror("获取失败", f"获取聊天列表时出错：{str(e)}")

    def get_all_messages(self):
        """获取当前聊天记录"""
        if not self.check_connection():
            return

        try:
            save_pic = self.save_pic_var.get()
            messages = self.wx.GetAllMessage(savepic=save_pic)

            self.log_message(f"获取到 {len(messages)} 条聊天记录")

            # 格式化显示消息
            msg_text = f"聊天记录 (共{len(messages)}条):\n" + "="*50 + "\n"
            for msg in messages[-20:]:  # 只显示最近20条
                if len(msg) >= 3:
                    sender = msg[0] if msg[0] != 'Self' else '我'
                    content = msg[1]
                    msg_text += f"[{sender}]: {content}\n"

            self.display_messages(msg_text)

        except Exception as e:
            self.log_message(f"获取聊天记录失败：{str(e)}")
            messagebox.showerror("获取失败", f"获取聊天记录时出错：{str(e)}")

    def get_new_messages(self):
        """获取新消息"""
        if not self.check_connection():
            return

        try:
            new_messages = self.wx.GetNextNewMessage(savepic=self.save_pic_var.get())

            if new_messages:
                self.log_message("获取到新消息")
                msg_text = "新消息:\n" + "="*30 + "\n"
                for chat, messages in new_messages.items():
                    msg_text += f"来自 {chat}:\n"
                    for msg in messages:
                        if len(msg) >= 2:
                            sender = msg[0] if msg[0] != 'Self' else '我'
                            content = msg[1]
                            msg_text += f"  [{sender}]: {content}\n"
                    msg_text += "\n"
                self.display_messages(msg_text)
            else:
                self.log_message("暂无新消息")
                self.display_messages("暂无新消息")

        except Exception as e:
            self.log_message(f"获取新消息失败：{str(e)}")
            messagebox.showerror("获取失败", f"获取新消息时出错：{str(e)}")

    def display_messages(self, text):
        """在消息显示区域显示文本"""
        self.messages_text.config(state=tk.NORMAL)
        self.messages_text.delete(1.0, tk.END)
        self.messages_text.insert(tk.END, text)
        self.messages_text.config(state=tk.DISABLED)
        self.messages_text.see(tk.END)

    # ==================== 文件操作相关方法 ====================
    def add_files(self):
        """添加文件"""
        files = filedialog.askopenfilenames(
            title="选择要发送的文件",
            filetypes=[("所有文件", "*.*"), ("图片文件", "*.jpg;*.png;*.gif;*.bmp"),
                      ("文档文件", "*.pdf;*.doc;*.docx;*.txt"), ("压缩文件", "*.zip;*.rar")]
        )
        for file in files:
            self.file_listbox.insert(tk.END, file)

    def add_folder(self):
        """添加文件夹中的所有文件"""
        folder = filedialog.askdirectory(title="选择文件夹")
        if folder:
            for root, _, files in os.walk(folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    self.file_listbox.insert(tk.END, file_path)

    def send_files(self):
        """发送文件"""
        if not self.check_connection():
            return

        try:
            recipient = self.file_recipient_var.get().strip()
            if not recipient:
                messagebox.showwarning("输入错误", "请输入接收人！")
                return

            files = list(self.file_listbox.get(0, tk.END))
            if not files:
                messagebox.showwarning("选择错误", "请选择要发送的文件！")
                return

            self.wx.SendFiles(files, recipient)
            self.log_message(f"已向 {recipient} 发送 {len(files)} 个文件")
            messagebox.showinfo("发送成功", f"已向 {recipient} 发送 {len(files)} 个文件")

        except Exception as e:
            self.log_message(f"发送文件失败：{str(e)}")
            messagebox.showerror("发送失败", f"发送文件时出错：{str(e)}")

    def batch_send_files(self):
        """批量发送文件"""
        if not self.check_connection():
            return

        try:
            recipients_text = self.batch_recipients_text.get(1.0, tk.END).strip()
            recipients = [r.strip() for r in recipients_text.split('\n') if r.strip()]

            if not recipients:
                messagebox.showwarning("输入错误", "请输入接收人列表！")
                return

            files = list(self.file_listbox.get(0, tk.END))
            if not files:
                messagebox.showwarning("选择错误", "请选择要发送的文件！")
                return

            success_count = 0
            for recipient in recipients:
                try:
                    self.wx.SendFiles(files, recipient)
                    success_count += 1
                    self.log_message(f"已向 {recipient} 发送文件")
                except Exception as e:
                    self.log_message(f"向 {recipient} 发送文件失败：{str(e)}")

            messagebox.showinfo("批量发送完成", f"成功向 {success_count}/{len(recipients)} 个联系人发送文件")

        except Exception as e:
            self.log_message(f"批量发送文件失败：{str(e)}")
            messagebox.showerror("发送失败", f"批量发送文件时出错：{str(e)}")

    def batch_send_messages(self):
        """批量发送消息"""
        if not self.check_connection():
            return

        try:
            recipients_text = self.batch_recipients_text.get(1.0, tk.END).strip()
            recipients = [r.strip() for r in recipients_text.split('\n') if r.strip()]

            if not recipients:
                messagebox.showwarning("输入错误", "请输入接收人列表！")
                return

            message = self.message_text.get(1.0, tk.END).strip()
            if not message:
                messagebox.showwarning("输入错误", "请输入要发送的消息内容！")
                return

            success_count = 0
            for recipient in recipients:
                try:
                    self.wx.SendMsg(message, recipient)
                    success_count += 1
                    self.log_message(f"已向 {recipient} 发送消息")
                    time.sleep(1)  # 避免发送过快
                except Exception as e:
                    self.log_message(f"向 {recipient} 发送消息失败：{str(e)}")

            messagebox.showinfo("批量发送完成", f"成功向 {success_count}/{len(recipients)} 个联系人发送消息")

        except Exception as e:
            self.log_message(f"批量发送消息失败：{str(e)}")
            messagebox.showerror("发送失败", f"批量发送消息时出错：{str(e)}")

    # ==================== 好友管理相关方法 ====================
    def get_new_friends(self):
        """获取新好友申请"""
        if not self.check_connection():
            return

        try:
            new_friends = self.wx.GetNewFriends()

            # 清空现有列表
            for item in self.new_friends_tree.get_children():
                self.new_friends_tree.delete(item)

            # 添加新好友申请
            for friend in new_friends:
                status = "可接受" if friend.acceptable else "已处理"
                self.new_friends_tree.insert("", tk.END, values=(friend.name, friend.msg, status))

            self.log_message(f"获取到 {len(new_friends)} 个新好友申请")
            messagebox.showinfo("获取成功", f"获取到 {len(new_friends)} 个新好友申请")

        except Exception as e:
            self.log_message(f"获取新好友申请失败：{str(e)}")
            messagebox.showerror("获取失败", f"获取新好友申请时出错：{str(e)}")

    def batch_accept_friends(self):
        """批量接受好友申请"""
        if not self.check_connection():
            return

        try:
            new_friends = self.wx.GetNewFriends()
            remark_template = self.friend_remark_var.get().strip()
            tags_text = self.friend_tags_var.get().strip()
            tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()] if tags_text else None

            success_count = 0
            for friend in new_friends:
                if friend.acceptable:
                    try:
                        remark = remark_template.replace("{name}", friend.name) if remark_template else None
                        friend.Accept(remark=remark, tags=tags)
                        success_count += 1
                        self.log_message(f"已接受好友申请：{friend.name}")
                    except Exception as e:
                        self.log_message(f"接受好友申请失败 {friend.name}：{str(e)}")

            messagebox.showinfo("批量接受完成", f"成功接受 {success_count} 个好友申请")
            self.get_new_friends()  # 刷新列表

        except Exception as e:
            self.log_message(f"批量接受好友申请失败：{str(e)}")
            messagebox.showerror("操作失败", f"批量接受好友申请时出错：{str(e)}")

    def get_all_friends(self):
        """获取所有好友列表"""
        if not self.check_connection():
            return

        try:
            friends = self.wx.GetAllFriends()

            # 清空现有列表
            for item in self.friends_tree.get_children():
                self.friends_tree.delete(item)

            # 添加好友信息
            for friend in friends:
                nickname = friend.get('nickname', '')
                remark = friend.get('remark', '') or ''
                tags = ', '.join(friend.get('tags', [])) if friend.get('tags') else ''
                self.friends_tree.insert("", tk.END, values=(nickname, remark, tags))

            self.log_message(f"获取到 {len(friends)} 个好友")
            messagebox.showinfo("获取成功", f"获取到 {len(friends)} 个好友")

        except Exception as e:
            self.log_message(f"获取好友列表失败：{str(e)}")
            messagebox.showerror("获取失败", f"获取好友列表时出错：{str(e)}")

    def search_friends(self):
        """搜索好友"""
        if not self.check_connection():
            return

        try:
            keyword = self.friend_search_var.get().strip()
            if not keyword:
                messagebox.showwarning("输入错误", "请输入搜索关键词！")
                return

            friends = self.wx.GetAllFriends(keywords=keyword)

            # 清空现有列表
            for item in self.friends_tree.get_children():
                self.friends_tree.delete(item)

            # 添加搜索结果
            for friend in friends:
                nickname = friend.get('nickname', '')
                remark = friend.get('remark', '') or ''
                tags = ', '.join(friend.get('tags', [])) if friend.get('tags') else ''
                self.friends_tree.insert("", tk.END, values=(nickname, remark, tags))

            self.log_message(f"搜索到 {len(friends)} 个匹配的好友")
            messagebox.showinfo("搜索完成", f"搜索到 {len(friends)} 个匹配的好友")

        except Exception as e:
            self.log_message(f"搜索好友失败：{str(e)}")
            messagebox.showerror("搜索失败", f"搜索好友时出错：{str(e)}")

    # ==================== 监听相关方法 ====================
    def add_listen_target(self):
        """添加监听目标"""
        target = self.listen_target_var.get().strip()
        if not target:
            messagebox.showwarning("输入错误", "请输入监听对象名称！")
            return

        if target not in self.listen_targets:
            self.listen_targets.append(target)
            self.listen_listbox.insert(tk.END, target)
            self.listen_target_var.set("")
            self.log_message(f"已添加监听目标：{target}")
        else:
            messagebox.showwarning("重复添加", "该监听目标已存在！")

    def start_listening(self):
        """开始监听"""
        if not self.check_connection():
            return

        if not self.listen_targets:
            messagebox.showwarning("设置错误", "请先添加监听目标！")
            return

        try:
            # 添加监听对象到wxauto
            for target in self.listen_targets:
                self.wx.AddListenChat(target, savepic=True)

            self.is_listening = True
            self.start_listen_btn.config(state=tk.DISABLED)
            self.stop_listen_btn.config(state=tk.NORMAL)

            # 启动监听线程
            self.listen_thread = threading.Thread(target=self.listen_loop, daemon=True)
            self.listen_thread.start()

            self.log_message("开始监听消息...")
            self.log_listen("监听已启动")

        except Exception as e:
            self.log_message(f"启动监听失败：{str(e)}")
            messagebox.showerror("启动失败", f"启动监听时出错：{str(e)}")

    def stop_listening(self):
        """停止监听"""
        self.is_listening = False
        self.start_listen_btn.config(state=tk.NORMAL)
        self.stop_listen_btn.config(state=tk.DISABLED)
        self.log_message("监听已停止")
        self.log_listen("监听已停止")

    def listen_loop(self):
        """监听循环"""
        try:
            interval = float(self.listen_interval_var.get())
        except:
            interval = 3.0

        while self.is_listening:
            try:
                if self.wx:
                    msgs = self.wx.GetListenMessage()
                    if msgs:
                        for chat, messages in msgs.items():
                            for msg in messages:
                                if len(msg) >= 2:
                                    sender = msg[0] if msg[0] != 'Self' else '我'
                                    content = msg[1]
                                    self.log_listen(f"[{chat}] {sender}: {content}")

                                    # 检查自动回复
                                    if self.auto_reply_enabled.get() and sender != '我':
                                        self.check_auto_reply(chat, content)

                time.sleep(interval)
            except Exception as e:
                self.log_message(f"监听过程中出错：{str(e)}")
                time.sleep(interval)

    def add_reply_rule(self):
        """添加回复规则"""
        keyword = self.keyword_var.get().strip()
        reply = self.reply_var.get().strip()

        if not keyword or not reply:
            messagebox.showwarning("输入错误", "请输入关键词和回复内容！")
            return

        self.reply_rules[keyword] = reply
        self.reply_rules_tree.insert("", tk.END, values=(keyword, reply))

        self.keyword_var.set("")
        self.reply_var.set("")
        self.log_message(f"已添加回复规则：{keyword} -> {reply}")

    def check_auto_reply(self, chat, content):
        """检查并执行自动回复"""
        for keyword, reply in self.reply_rules.items():
            if keyword in content:
                try:
                    self.wx.SendMsg(reply, chat)
                    self.log_listen(f"自动回复 [{chat}]: {reply}")
                    break
                except Exception as e:
                    self.log_message(f"自动回复失败：{str(e)}")

    def log_listen(self, message):
        """记录监听日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"

        self.listen_log_text.config(state=tk.NORMAL)
        self.listen_log_text.insert(tk.END, log_msg)
        self.listen_log_text.see(tk.END)
        self.listen_log_text.config(state=tk.DISABLED)

    # ==================== 工具相关方法 ====================
    def get_group_members(self):
        """获取群成员"""
        if not self.check_connection():
            return

        try:
            members = self.wx.GetGroupMembers()
            if members:
                members_text = f"群成员列表 (共{len(members)}人):\n" + "="*40 + "\n"
                for i, member in enumerate(members, 1):
                    members_text += f"{i:3d}. {member}\n"

                self.group_members_text.config(state=tk.NORMAL)
                self.group_members_text.delete(1.0, tk.END)
                self.group_members_text.insert(tk.END, members_text)
                self.group_members_text.config(state=tk.DISABLED)

                self.log_message(f"获取到 {len(members)} 个群成员")
                messagebox.showinfo("获取成功", f"获取到 {len(members)} 个群成员")
            else:
                messagebox.showinfo("提示", "当前不在群聊窗口或获取失败")

        except Exception as e:
            self.log_message(f"获取群成员失败：{str(e)}")
            messagebox.showerror("获取失败", f"获取群成员时出错：{str(e)}")

    def switch_to_contact(self):
        """切换到通讯录"""
        if not self.check_connection():
            return

        try:
            self.wx.SwitchToContact()
            self.log_message("已切换到通讯录页面")
            messagebox.showinfo("切换成功", "已切换到通讯录页面")
        except Exception as e:
            self.log_message(f"切换到通讯录失败：{str(e)}")
            messagebox.showerror("切换失败", f"切换到通讯录时出错：{str(e)}")

    def switch_to_chat(self):
        """切换到聊天"""
        if not self.check_connection():
            return

        try:
            self.wx.SwitchToChat()
            self.log_message("已切换到聊天页面")
            messagebox.showinfo("切换成功", "已切换到聊天页面")
        except Exception as e:
            self.log_message(f"切换到聊天失败：{str(e)}")
            messagebox.showerror("切换失败", f"切换到聊天时出错：{str(e)}")

    def check_new_message(self):
        """检查新消息"""
        if not self.check_connection():
            return

        try:
            has_new = self.wx.CheckNewMessage()
            if has_new:
                self.log_message("检测到新消息")
                messagebox.showinfo("检查结果", "有新消息")
            else:
                self.log_message("暂无新消息")
                messagebox.showinfo("检查结果", "暂无新消息")
        except Exception as e:
            self.log_message(f"检查新消息失败：{str(e)}")
            messagebox.showerror("检查失败", f"检查新消息时出错：{str(e)}")

    def get_current_chat(self):
        """获取当前聊天窗口"""
        if not self.check_connection():
            return

        try:
            current_chat = self.wx.CurrentChat()
            if current_chat:
                self.log_message(f"当前聊天窗口：{current_chat}")
                messagebox.showinfo("当前聊天窗口", f"当前聊天窗口：{current_chat}")
            else:
                messagebox.showinfo("提示", "未检测到当前聊天窗口")
        except Exception as e:
            self.log_message(f"获取当前聊天窗口失败：{str(e)}")
            messagebox.showerror("获取失败", f"获取当前聊天窗口时出错：{str(e)}")

    def load_more_messages(self):
        """加载更多消息"""
        if not self.check_connection():
            return

        try:
            result = self.wx.LoadMoreMessage()
            if result:
                self.log_message("成功加载更多消息")
                messagebox.showinfo("加载成功", "成功加载更多消息")
            else:
                self.log_message("没有更多消息可加载")
                messagebox.showinfo("提示", "没有更多消息可加载")
        except Exception as e:
            self.log_message(f"加载更多消息失败：{str(e)}")
            messagebox.showerror("加载失败", f"加载更多消息时出错：{str(e)}")

    # ==================== 日志相关方法 ====================
    def log_message(self, message):
        """记录日志消息"""
        if not self.enable_log_var.get():
            return

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"

        # 显示在状态文本框
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, log_msg)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)

        # 保存到文件
        if self.save_log_var.get():
            try:
                log_file = os.path.join(os.path.dirname(__file__), "wxauto_ui.log")
                with open(log_file, "a", encoding="utf-8") as f:
                    f.write(log_msg)
            except Exception as e:
                print(f"保存日志失败：{e}")


def main():
    """主程序入口"""
    root = tk.Tk()

    # 设置窗口图标（如果有的话）
    try:
        # 可以在这里设置窗口图标
        # root.iconbitmap("icon.ico")
        pass
    except:
        pass

    # 创建应用实例
    app = WxAutoUI(root)

    # 设置窗口关闭事件
    def on_closing():
        if app.is_listening:
            app.stop_listening()
        if app.wx:
            app.disconnect_wechat()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    main()
