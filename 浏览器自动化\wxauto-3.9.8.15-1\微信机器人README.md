# 微信聊天机器人项目

## 项目概述

基于 wxauto 库和 OpenRouter API 开发的智能微信聊天机器人，实现了实时监听微信消息并通过大模型 API 生成智能回复的功能。

## 项目结构

```
wxauto-3.9.8.15-1/
├── wxbot.py                     # 完整版机器人（推荐）
├── simple_wxbot.py              # 简化版机器人
├── 启动微信机器人.bat            # 机器人启动脚本
├── 微信机器人使用说明.md         # 详细使用说明
├── 微信机器人README.md          # 项目说明（本文件）
├── 微信机器人.md                # 原始需求文档
├── bot_config_template.json     # 配置文件模板
├── bot_config.json              # 配置文件（运行时自动生成）
├── wxbot.log                    # 运行日志（运行时生成）
└── requirements.txt             # 依赖库列表（已更新）
```

## 功能特性

### 🤖 智能对话
- **OpenRouter API 集成**: 支持多种大模型（qwen、GPT、Claude等）
- **上下文理解**: 智能理解对话内容并生成合适回复
- **自定义提示词**: 可配置机器人的回复风格和行为

### 💬 消息监听
- **实时监听**: 监听指定群聊或好友的新消息
- **关键词触发**: 通过特定关键词（如@wxBOT）触发回复
- **多对象支持**: 同时监听多个群聊或好友

### 🔧 灵活配置
- **JSON配置**: 通过配置文件轻松修改参数
- **权限控制**: 可分别控制群聊和私聊回复
- **参数调节**: 监听间隔、回复长度等可自定义

### 📝 日志系统
- **详细记录**: 记录所有消息和回复内容
- **双重输出**: 控制台显示 + 文件保存
- **便于调试**: 方便排查问题和监控运行状态

## 版本说明

### 完整版 (wxbot.py)
- **功能丰富**: 包含完整的配置系统、日志记录、错误处理
- **易于配置**: 通过JSON文件进行配置，支持热更新
- **稳定可靠**: 完善的异常处理和错误恢复机制
- **推荐使用**: 适合正式部署和长期运行

### 简化版 (simple_wxbot.py)
- **代码简洁**: 基于原始文档的示例代码
- **快速上手**: 最少配置即可运行
- **学习友好**: 代码结构清晰，便于理解和修改
- **测试推荐**: 适合快速测试和学习使用

## 快速开始

### 1. 环境准备
```bash
# 确保已安装Python 3.8+
python --version

# 安装依赖（如果尚未安装）
pip install -r requirements.txt
```

### 2. 配置设置
编辑配置文件或直接修改代码中的参数：
- **API Key**: 设置你的 OpenRouter API Key
- **监听对象**: 配置要监听的群聊或好友名称
- **触发关键词**: 设置触发回复的关键词

### 3. 启动机器人
```bash
# 方式一：双击启动脚本
双击 "启动微信机器人.bat"

# 方式二：直接运行Python文件
python wxbot.py          # 完整版
python simple_wxbot.py   # 简化版
```

### 4. 使用测试
在微信中向监听对象发送包含触发关键词的消息，如：
```
@wxBOT 你好，请介绍一下自己
```

## 配置说明

### API Key 获取
1. 访问 [OpenRouter](https://openrouter.ai/) 官网
2. 注册账号并获取 API Key
3. 将 API Key 配置到机器人中

### 监听对象设置
- **群聊**: 使用完整的群聊名称
- **好友**: 使用好友的备注名或昵称
- **测试**: 建议先用"文件传输助手"进行测试

### 触发关键词
- 默认使用 `@wxBOT` 作为触发关键词
- 可以修改为任意自定义关键词
- 支持多个关键词（需修改代码逻辑）

## 支持的模型

OpenRouter 平台支持的主要模型：
- `qwen/qwen-32b-chat` - 通义千问（免费，推荐）
- `openai/gpt-4o` - GPT-4 Omni
- `openai/gpt-3.5-turbo` - GPT-3.5 Turbo
- `anthropic/claude-3-sonnet` - Claude 3 Sonnet

## 注意事项

### ⚠️ 重要提醒
- **合规使用**: 仅供学习交流，请勿用于非法用途
- **API费用**: 使用付费模型会产生费用，请注意余额
- **微信限制**: 遵守微信使用条款，避免频繁操作

### 🔧 技术要求
- **微信版本**: 建议使用 3.9.8.15 版本
- **网络连接**: 需要稳定的网络连接调用API
- **系统环境**: Windows 10/11，Python 3.8+

### 💡 使用建议
- **测试优先**: 先在测试环境验证功能
- **合理间隔**: 设置适当的监听间隔，避免过于频繁
- **日志监控**: 定期查看日志了解运行状态
- **备份配置**: 保存好配置文件和重要设置

## 故障排除

### 常见问题
1. **连接失败**: 检查微信是否登录，版本是否兼容
2. **API错误**: 验证API Key是否正确，余额是否充足
3. **无法回复**: 确认触发关键词和监听对象配置
4. **回复异常**: 检查网络连接和模型可用性

### 调试方法
- 查看控制台输出的实时日志
- 检查 `wxbot.log` 文件中的详细记录
- 使用"文件传输助手"进行功能测试
- 逐步排查配置和网络问题

## 开发扩展

### 自定义功能
- **提示词优化**: 修改 `PRESET_PROMPT` 调整回复风格
- **功能扩展**: 添加图片识别、文件处理等功能
- **多模型支持**: 实现模型切换和负载均衡
- **数据库集成**: 添加对话历史存储功能

### 代码结构
- **模块化设计**: 核心功能分离，便于维护
- **配置驱动**: 通过配置文件控制行为
- **异常处理**: 完善的错误处理和恢复机制
- **日志系统**: 详细的运行状态记录

## 许可证

本项目基于 MIT 许可证开源。

## 免责声明

本工具仅供学习交流使用，请勿用于非法用途和商业用途！使用时请遵守微信使用条款和相关法律法规。如因使用本工具产生任何法律纠纷，均与作者无关。

---

**版本**: 1.0.0  
**基于**: wxauto 3.9.8.15 + OpenRouter API  
**文档**: 基于 `微信机器人.md` 需求文档开发
