#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信聊天机器人 - 基于 OpenRouter API
使用 wxauto 库监听微信消息，通过 OpenRouter 平台的大模型 API 生成智能回复

Author: wxauto Bot Generator
Version: 1.0.0
Based on: wxauto 3.9.8.15
"""

# 导入相关库
from wxauto import WeChat
from openai import OpenAI
import time
import json
import os
from datetime import datetime

# 配置文件路径
CONFIG_FILE = "bot_config.json"

# 预设提示词
PRESET_PROMPT = (
    "你是一个友善且轻松的微信群聊机器人，擅长用平和而又有趣的方式回应群友的问题。"
    "你的回答要简洁明了，但不失温暖，让大家感到轻松愉快。"
    "对简单问题，提供直接的答案，但不失亲切感。对复杂问题，先简要概括，再给出具体建议。"
    "偶尔可以用一些生活化的表达方式，增加互动感。"
    "遇到无聊或无意义的提问，机智地化解尴尬。"
    "回复长度控制在100字以内，保持简洁有效。"
)

class WeChatBot:
    def __init__(self):
        """初始化微信机器人"""
        self.wx = None
        self.client = None
        self.config = self.load_config()
        self.is_running = False
        
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "api_key": "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",
            "model": "qwen/qwen-32b-chat",
            "site_url": "https://your-site.com",
            "site_name": "WxBot",
            "listen_list": ["文件传输助手"],
            "trigger_keyword": "@wxBOT",
            "wait_interval": 1,
            "max_reply_length": 100,
            "enable_group_chat": True,
            "enable_private_chat": True,
            "log_messages": True
        }
        
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            except Exception as e:
                print(f"配置文件加载失败，使用默认配置: {e}")
                return default_config
        else:
            # 创建默认配置文件
            self.save_config(default_config)
            return default_config
    
    def save_config(self, config):
        """保存配置文件"""
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"配置文件已保存到: {CONFIG_FILE}")
        except Exception as e:
            print(f"配置文件保存失败: {e}")
    
    def init_wechat(self):
        """初始化微信连接"""
        try:
            print("正在连接微信...")
            self.wx = WeChat()
            print(f"微信连接成功！当前用户：{self.wx.nickname}")
            return True
        except Exception as e:
            print(f"微信连接失败：{e}")
            return False
    
    def init_openrouter(self):
        """初始化 OpenRouter 客户端"""
        try:
            self.client = OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=self.config["api_key"],
            )
            print("OpenRouter 客户端初始化成功")
            return True
        except Exception as e:
            print(f"OpenRouter 客户端初始化失败：{e}")
            return False
    
    def setup_listeners(self):
        """设置监听对象"""
        try:
            print("正在设置监听对象...")
            for chat_name in self.config["listen_list"]:
                self.wx.AddListenChat(who=chat_name, savepic=False)
                print(f"已添加监听对象：{chat_name}")
            print(f"共设置了 {len(self.config['listen_list'])} 个监听对象")
            return True
        except Exception as e:
            print(f"设置监听对象失败：{e}")
            return False
    
    def get_ai_reply(self, content, chat_type="group"):
        """获取 AI 回复"""
        try:
            # 构建消息
            system_prompt = PRESET_PROMPT
            if chat_type == "private":
                system_prompt += "\n注意：这是私聊对话，回复要更加个人化和亲切。"
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": content},
            ]
            
            # 调用 OpenRouter API
            completion = self.client.chat.completions.create(
                extra_headers={
                    "HTTP-Referer": self.config["site_url"],
                    "X-Title": self.config["site_name"],
                },
                model=self.config["model"],
                messages=messages,
                max_tokens=self.config["max_reply_length"],
                temperature=0.7
            )
            
            reply = completion.choices[0].message.content
            return reply.strip()
            
        except Exception as e:
            print(f"获取 AI 回复失败：{e}")
            return "抱歉，我现在有点忙，稍后再回复你哦～"
    
    def should_reply(self, content, chat_type):
        """判断是否应该回复消息"""
        # 检查触发关键词
        if self.config["trigger_keyword"] not in content:
            return False
        
        # 检查聊天类型权限
        if chat_type == "group" and not self.config["enable_group_chat"]:
            return False
        if chat_type == "private" and not self.config["enable_private_chat"]:
            return False
        
        return True
    
    def log_message(self, who, content, reply=None):
        """记录消息日志"""
        if not self.config["log_messages"]:
            return
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] 【{who}】: {content}"
        if reply:
            log_entry += f" -> 回复: {reply}"
        
        print(log_entry)
        
        # 保存到日志文件
        try:
            with open("wxbot.log", "a", encoding="utf-8") as f:
                f.write(log_entry + "\n")
        except Exception as e:
            print(f"日志保存失败: {e}")
    
    def run(self):
        """运行机器人"""
        print("="*50)
        print("微信聊天机器人启动中...")
        print("="*50)
        
        # 初始化各个组件
        if not self.init_wechat():
            return False
        
        if not self.init_openrouter():
            return False
        
        if not self.setup_listeners():
            return False
        
        print("\n机器人启动成功！开始监听消息...")
        print(f"触发关键词：{self.config['trigger_keyword']}")
        print(f"监听间隔：{self.config['wait_interval']} 秒")
        print("按 Ctrl+C 停止机器人\n")
        
        self.is_running = True
        
        try:
            # 持续监听消息
            while self.is_running:
                msgs = self.wx.GetListenMessage()
                
                for chat in msgs:
                    who = chat.who
                    one_msgs = msgs.get(chat)
                    
                    for msg in one_msgs:
                        try:
                            # 获取消息信息
                            content = msg.content
                            sender = msg.sender if hasattr(msg, 'sender') else 'Unknown'
                            
                            # 跳过自己发送的消息
                            if sender == 'Self' or sender == self.wx.nickname:
                                continue
                            
                            # 判断聊天类型
                            chat_type = "group" if hasattr(msg, 'chattype') and msg.chattype == "group" else "private"
                            
                            # 记录收到的消息
                            self.log_message(who, content)
                            
                            # 判断是否需要回复
                            if self.should_reply(content, chat_type):
                                print(f"触发回复 - 【{who}】({chat_type}): {content}")
                                
                                # 获取 AI 回复
                                reply = self.get_ai_reply(content, chat_type)
                                
                                # 发送回复
                                if hasattr(msg, 'quote'):
                                    msg.quote(reply)  # 引用回复
                                else:
                                    self.wx.SendMsg(reply, who)  # 普通回复
                                
                                # 记录回复日志
                                self.log_message(who, content, reply)
                                print(f"已回复 - 【{who}】: {reply}\n")
                                
                        except Exception as e:
                            print(f"处理消息时出错: {e}")
                            continue
                
                time.sleep(self.config["wait_interval"])
                
        except KeyboardInterrupt:
            print("\n\n机器人已停止运行")
            self.is_running = False
        except Exception as e:
            print(f"机器人运行出错: {e}")
            self.is_running = False
        
        return True


def main():
    """主函数"""
    bot = WeChatBot()
    
    # 显示配置信息
    print("当前配置:")
    print(f"- API Key: {bot.config['api_key'][:20]}...")
    print(f"- 模型: {bot.config['model']}")
    print(f"- 监听对象: {bot.config['listen_list']}")
    print(f"- 触发关键词: {bot.config['trigger_keyword']}")
    print()
    
    # 运行机器人
    bot.run()


if __name__ == "__main__":
    main()
