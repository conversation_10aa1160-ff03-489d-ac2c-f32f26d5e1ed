# wxauto UI界面使用说明

## 概述

wxauto UI界面是基于wxauto库开发的图形化微信自动化操作工具，提供了友好的用户界面来执行各种微信自动化任务。

## 启动方式

### 方式一：双击批处理文件
直接双击 `启动wxauto界面.bat` 文件即可启动

### 方式二：命令行启动
```bash
cd wxauto-3.9.8.15-1
python wxauto.py
```

## 功能页面说明

### 1. 🔗 连接状态页面
**主要功能：**
- 连接/断开微信客户端
- 选择界面语言（简体中文/繁体中文/英文）
- 查看连接状态和日志
- 显示版本信息

**使用步骤：**
1. 确保微信客户端已登录
2. 选择合适的界面语言
3. 点击"连接微信"按钮
4. 查看连接状态确认连接成功

### 2. 💬 消息操作页面
**主要功能：**
- 发送文本消息
- 获取聊天列表
- 获取聊天记录
- 获取新消息

**使用方法：**
- **发送消息**：输入接收人和消息内容，点击发送
- **获取聊天列表**：点击按钮查看当前所有聊天对象
- **获取聊天记录**：获取当前聊天窗口的历史消息
- **自动保存图片**：勾选后会自动保存聊天中的图片

### 3. 📁 文件操作页面
**主要功能：**
- 发送单个或多个文件
- 批量发送文件给多个联系人
- 批量发送消息给多个联系人

**使用方法：**
- **添加文件**：点击"添加文件"选择要发送的文件
- **添加文件夹**：点击"添加文件夹"添加整个文件夹中的文件
- **批量发送**：在接收人列表中每行输入一个联系人名称

### 4. 👥 好友管理页面
**主要功能：**
- 获取新好友申请
- 批量接受好友申请
- 获取所有好友列表
- 搜索好友

**使用方法：**
- **处理好友申请**：设置备注和标签模板，批量接受好友申请
- **好友列表管理**：查看所有好友信息，支持关键词搜索
- **备注设置**：可使用 `{name}` 占位符自动替换为好友昵称

### 5. 🔄 监听回复页面
**主要功能：**
- 添加监听对象
- 实时监听消息
- 设置自动回复规则
- 查看监听日志

**使用方法：**
1. **添加监听对象**：输入要监听的好友或群名称
2. **设置自动回复**：添加关键词和对应的回复内容
3. **开始监听**：点击开始监听，程序会自动监控新消息
4. **自动回复**：当收到包含关键词的消息时自动回复

### 6. 🔧 工具设置页面
**主要功能：**
- 群管理工具（获取群成员等）
- 系统工具（检查新消息、切换页面等）
- 设置监听间隔和日志选项
- 查看软件信息

## 使用技巧

### 1. 联系人名称输入
- 建议使用完整的联系人名称或备注
- 群聊名称会显示成员数量，如"工作群(50)"
- 可以使用"文件传输助手"进行测试

### 2. 批量操作
- 在批量发送时，每行输入一个联系人名称
- 程序会自动处理发送间隔，避免发送过快
- 建议先用少量联系人测试

### 3. 监听设置
- 监听间隔建议设置为3-5秒
- 自动回复关键词支持部分匹配
- 可以设置多个关键词规则

### 4. 错误处理
- 如果连接失败，请检查微信版本是否为3.9.8.15
- 如果功能异常，可以尝试重新连接微信
- 查看日志信息了解具体错误原因

## 注意事项

### 安全提醒
- 本工具仅供学习交流使用
- 请勿用于非法用途和商业用途
- 使用时请遵守微信使用条款

### 系统要求
- Windows 10/11/Server 2016+
- Python 3.8+
- 微信客户端 3.9.8.15（推荐）

### 使用限制
- 需要微信客户端已登录
- 部分功能依赖微信界面元素
- 不支持微信网页版

## 常见问题

**Q: 连接微信失败怎么办？**
A: 请确保微信客户端已登录，版本为3.9.8.15，并且没有其他程序占用微信窗口。

**Q: 发送消息失败？**
A: 检查联系人名称是否正确，确保该联系人在聊天列表中。

**Q: 监听功能不工作？**
A: 确保已添加监听对象，并且微信窗口处于活动状态。

**Q: 自动回复不生效？**
A: 检查是否启用了自动回复功能，关键词设置是否正确。

## 技术支持

如遇到问题，请：
1. 查看程序日志了解错误信息
2. 确认微信版本和系统环境
3. 参考wxauto官方文档
4. 在GitHub提交Issue

---

*版本：3.9.8.15*  
*最后更新：2024年*
