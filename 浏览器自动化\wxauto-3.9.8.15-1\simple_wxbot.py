#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版微信聊天机器人 - 基于原文档示例代码
直接基于微信机器人.md文档中的示例代码实现

Author: Based on 微信机器人.md
Version: 1.0.0
"""

# 导入相关库
from wxauto import WeChat
from openai import OpenAI
import time

# 预设提示词
PRESET_PROMPT = (
    "你是一个友善且轻松的微信群聊机器人，擅长用平和而又有趣的方式回应群友的问题。"
    "你的回答要简洁明了，但不失温暖，让大家感到轻松愉快。"
    "对简单问题，提供直接的答案，但不失亲切感。对复杂问题，先简要概括，再给出具体建议。"
    "偶尔可以用一些生活化的表达方式，增加互动感。"
    "遇到无聊或无意义的提问，机智地化解尴尬。"
)

# 初始化 OpenRouter 客户端
client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key="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",
)

# 获取微信窗口对象
print("正在连接微信...")
wx = WeChat()
print(f"微信连接成功！当前用户：{wx.nickname}")

# 设置监听列表
listen_list = ["文件传输助手"]  # 这里可以修改为你要监听的群聊名称或好友名称

print("正在设置监听对象...")
# 循环添加监听对象
for i in listen_list:
    wx.AddListenChat(who=i, savepic=False)
    print(f"已添加监听对象：{i}")

# 定义获取 AI 回复的函数
def get_ai_reply(content):
    """获取AI回复"""
    try:
        messages = [
            {"role": "system", "content": PRESET_PROMPT},
            {"role": "user", "content": content},
        ]
        completion = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": "https://your-site.com",
                "X-Title": "WxBot",
            },
            model="qwen/qwen-32b-chat",  # 使用免费的qwen模型
            messages=messages
        )
        return completion.choices[0].message.content
    except Exception as e:
        print(f"获取AI回复失败: {e}")
        return "抱歉，我现在有点忙，稍后再回复你哦～"

print("\n机器人启动成功！开始监听消息...")
print("触发关键词：@wxBOT")
print("按 Ctrl+C 停止机器人\n")

# 持续监听消息
wait = 1
try:
    while True:
        msgs = wx.GetListenMessage()
        for chat in msgs:
            who = chat.who
            one_msgs = msgs.get(chat)
            for msg in one_msgs:
                try:
                    # 获取消息内容
                    content = msg.content
                    sender = getattr(msg, 'sender', 'Unknown')
                    
                    # 跳过自己发送的消息
                    if sender == 'Self':
                        continue
                    
                    print(f"【{who}】{sender}: {content}")
                    
                    # 检查是否包含触发关键词
                    if "@wxBOT" in content:
                        print(f"触发回复 - 【{who}】: {content}")
                        
                        # 获取AI回复
                        reply = get_ai_reply(content)
                        
                        # 发送回复
                        try:
                            # 尝试使用引用回复
                            if hasattr(msg, 'quote'):
                                msg.quote(reply)
                            else:
                                # 如果不支持引用，使用普通发送
                                wx.SendMsg(reply, who)
                        except:
                            # 备用方案：直接发送消息
                            wx.SendMsg(reply, who)
                        
                        print(f"已回复 - 【{who}】: {reply}\n")
                        
                except Exception as e:
                    print(f"处理消息时出错: {e}")
                    continue
        
        time.sleep(wait)
        
except KeyboardInterrupt:
    print("\n\n机器人已停止运行")
except Exception as e:
    print(f"机器人运行出错: {e}")

print("程序结束")
