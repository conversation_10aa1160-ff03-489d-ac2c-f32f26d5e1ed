好的，以下是基于上述代码和说明生成的步骤说明文档，以 Markdown 格式呈现：

# 微信聊天机器人部署指南（使用 OpenRouter API）

## 项目简介
本项目实现了一个基于 OpenRouter 平台大模型 API 的微信聊天机器人。机器人能够实时监听微信好友或群聊的新消息，并通过 OpenRouter 的 API 接口生成回复。

## 安装步骤

### 1. 安装依赖
安装项目所需的依赖库，包括 `wxauto` 和 `openai`。
```shell
pip install wxauto -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install openai
```

### 2. 配置 OpenRouter API
在代码中配置 OpenRouter 的 API Key 和模型版本。将 `<OPENROUTER_API_KEY>` 替换为你的 OpenRouter API Key（【qwen/qwen3-32b:free】：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），并将 `<YOUR_SITE_URL>` 和 `<YOUR_SITE_NAME>` 替换为你的网站信息（如果需要）。

### 3. 设置监听对象
在代码中设置需要监听的微信好友或群聊名称。例如：
```python
listen_list = ["群聊名称", "好友名称", "文件传输助手"]
```

### 4. 运行程序
运行程序后，登录微信并授权，机器人将开始监听消息并自动回复。

## 代码结构
- `README.md`：项目说明文档。
- `wxbot.py`：聊天机器人核心代码。

## 代码说明

### 1. 预设提示词
预设提示词定义了机器人的回答风格和行为，可以根据需要进行修改。

### 2. 初始化 OpenRouter 客户端
使用 OpenRouter 提供的 SDK 初始化客户端，并设置 API Key 和基础 URL。

### 3. 获取 AI 回复
通过调用 OpenRouter 的 API，发送请求并获取模型生成的回复内容。

### 4. 微信消息监听
通过 `wxauto` 库监听微信消息，并在满足条件时调用 AI 回复。

## 注意事项
- 确保 OpenRouter API Key 有效且版本兼容。
- 根据需要调整预设提示词以适应不同的聊天场景。
- 由于微信的限制，可能需要手动登录微信并授权。

## 示例代码
以下是完整的代码示例：

```python
# 导入相关库
from wxauto import WeChat
from openai import OpenAI
import time

# 预设提示词
PRESET_PROMPT = (
    "你是一个友善且轻松的微信群聊机器人，擅长用平和而又有趣的方式回应群友的问题。"
    "你的回答要简洁明了，但不失温暖，让大家感到轻松愉快。"
    "对简单问题，提供直接的答案，但不失亲切感。对复杂问题，先简要概括，再给出具体建议。"
    "偶尔可以用一些生活化的表达方式，增加互动感。"
    "遇到无聊或无意义的提问，机智地化解尴尬。"
)

# 初始化 OpenRouter 客户端
client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key="<OPENROUTER_API_KEY>",
)

# 获取微信窗口对象
wx = WeChat()

# 设置监听列表
listen_list = ["这里写群聊名称或者好友名称", "文件传输助手"]
# 循环添加监听对象
for i in listen_list:
    wx.AddListenChat(who=i, savepic=False)

# 定义获取 AI 回复的函数
def get_ai_reply(content):
    messages = [
        {"role": "system", "content": PRESET_PROMPT},
        {"role": "user", "content": content},
    ]
    completion = client.chat.completions.create(
        extra_headers={
            "HTTP-Referer": "<YOUR_SITE_URL>",
            "X-Title": "<YOUR_SITE_NAME>",
        },
        model="openai/gpt-4o",
        messages=messages
    )
    return completion.choices[0].message.content

# 持续监听消息
wait = 1
while True:
    msgs = wx.GetListenMessage()
    for chat in msgs:
        who = chat.who
        one_msgs = msgs.get(chat)
        for msg in one_msgs:
            msgtype = msg.type
            content = msg.content
            print(f"【{who}】：{content}")
            if msgtype == "friend" and "@wxBOT" in content:
                reply = get_ai_reply(content)
                msg.quote(reply)
    time.sleep(wait)
```